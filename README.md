# 🤖 Bot Tự Động Tác Vụ Android

Một ứng dụng Python thông minh giúp **tự động hóa các thao tác lặp đi lặp lại** trong ứng dụng Android bằng cách sử dụng **ADB + OpenCV**. Giao diện trực quan với 2 cột: **thiết bị ADB** và **danh sách ứng dụng**, gi<PERSON><PERSON> quản lý nhiều tác vụ theo thời gian đếm ngượ<PERSON>, hỗ trợ đa thiết bị và dễ mở rộng.
---
## 📌 Mục Tiêu Dự Án
- Tự động hóa các hành động lặp đi lặp lại trên ứng dụng Android (điểm danh, nhận quà, click nút, v.v.).
- Quản lý nhiều ứng dụng cùng lúc.
- <PERSON><PERSON><PERSON> lịch, đế<PERSON> ngược thời gian và chạy lại định kỳ.
- Gia<PERSON> di<PERSON>n trực quan, hỗ trợ theo dõi tiến trình.
---
## 🎯 Tính Năng Chính
- ✅ Kết nối và điều khiển thiết bị Android qua ADB.
- ✅ Nhận diện ảnh bằng OpenCV thay vì tọa độ cố định.
- ✅ Giao diện hiển thị **2 cột**:
  - Cột 1: Thiết bị Android đang kết nối.
  - Cột 2: Danh sách ứng dụng cấu hình, sắp xếp theo thời gian chạy gần nhất.
- ✅ Hiển thị cấu hình từng app: khoảng thời gian lặp, ảnh mẫu, log hoạt động.
- ✅ Hỗ trợ đa thiết bị / đa ứng dụng / đa tác vụ.
- ✅ Tạm dừng, kích hoạt lại từng tác vụ.
- ✅ Tắt quảng cáo của ứng dụng sau khi chạy hoàn tất
---

## 🖥️ Giao Diện (Concept UI)

```

╔═════════════╦════════════════════════════════════╗
║  📡 ADB Devices  ║      📱 Apps Đã Cấu Hình (Gần Nhất)     ║
╠═════════════╬════════════════════════════════════╣
║  emulator-5554 ║   \[📷] App Check-in (còn 5m30s)         ║
║  usb-device-1  ║   \[🎁] App Rewards  (chạy ngay)          ║
║               ║   \[🧼] App Cleanup (còn 1h02m)            ║
╚═════════════╩════════════════════════════════════╝

```

🖱️ Khi nhấn vào từng app:
- Hiển thị danh sách ảnh mẫu (`*.png`)
- Khoảng thời gian lặp (countdown)
- Log chi tiết: thời gian chạy gần nhất, trạng thái, lỗi nếu có

---

## ⚙️ Luồng Hoạt Động

1. **Kết nối thiết bị** qua `adb devices`.
2. **Chọn ứng dụng** để chạy tự động.
3. **Tác vụ được lên lịch** và chạy theo vòng lặp định kỳ.
4. **Chụp ảnh màn hình**, tìm kiếm ảnh mẫu, gửi lệnh tương tác.
5. **Tự động lặp lại** sau thời gian đếm ngược.
6. **Ghi log**, thống kê hoạt động từng app.
---
## 🧠 Công Nghệ Sử Dụng
* [x] **Python 3.7+**
* [x] [ADB (Android Debug Bridge)](https://developer.android.com/studio/command-line/adb)
* [x] **OpenCV** để nhận diện hình ảnh (`cv2.matchTemplate`)
* [x] **PyQt6** hoặc **Tkinter** cho giao diện 2 cột
* [x] Tùy chọn thêm: **OCR (Tesseract)** để nhận dạng chữ (nâng cao)
---
## ⏰ Quản Lý Tác Vụ Theo Thời Gian
* Mỗi tác vụ có `interval_seconds` → hệ thống sẽ tự động chạy lại sau đúng thời gian đó.
* Countdown realtime được hiển thị trong UI.
* Cho phép tạm dừng hoặc kích hoạt lại từng tác vụ.
* Ghi lại `last_run` cho mỗi app để tính toán thời gian chạy tiếp theo.
* Khi thực hiện tác vụ, cập nhật `last_run` và `next_run` cho app đó.
* Kiểm tra xem tác vụ có thể thực hiện được hay không